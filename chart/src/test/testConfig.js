// 测试配置文件 - 用于验证应用功能
import { getDefaultTimeConfig, validateTimeConfig } from '../utils/timeUtils.js';
import { generateChartData, ATTACK_TYPES, FLUCTUATION_LEVELS } from '../utils/chartUtils.js';

// 测试默认配置
export const testDefaultConfig = () => {
  console.log('=== 测试默认时间配置 ===');
  const defaultConfig = getDefaultTimeConfig();
  console.log('默认配置:', defaultConfig);
  
  const validation = validateTimeConfig(defaultConfig);
  console.log('配置验证:', validation);
  
  return validation.isValid;
};

// 测试 DDoS 攻击数据生成
export const testDDoSGeneration = () => {
  console.log('=== 测试 DDoS 攻击数据生成 ===');
  const config = {
    attackType: 'DDOS',
    peakValue: 500,
    fluctuationLevel: 'HIGH',
    domain: '',
    ...getDefaultTimeConfig()
  };
  
  const chartData = generateChartData(config);
  console.log('DDoS 图表数据:', chartData);
  
  return chartData.datasets[0].data.length > 0;
};

// 测试 CC 攻击数据生成
export const testCCGeneration = () => {
  console.log('=== 测试 CC 攻击数据生成 ===');
  const config = {
    attackType: 'CC',
    peakValue: 5000000,
    fluctuationLevel: 'MEDIUM',
    domain: 'example.com',
    ...getDefaultTimeConfig()
  };
  
  const chartData = generateChartData(config);
  console.log('CC 图表数据:', chartData);
  
  return chartData.datasets[0].data.length > 0;
};

// 运行所有测试
export const runAllTests = () => {
  console.log('开始运行功能测试...');
  
  const tests = [
    { name: '默认配置测试', test: testDefaultConfig },
    { name: 'DDoS 数据生成测试', test: testDDoSGeneration },
    { name: 'CC 数据生成测试', test: testCCGeneration }
  ];
  
  const results = tests.map(({ name, test }) => {
    try {
      const result = test();
      console.log(`✅ ${name}: ${result ? '通过' : '失败'}`);
      return { name, passed: result };
    } catch (error) {
      console.error(`❌ ${name}: 错误 -`, error);
      return { name, passed: false, error };
    }
  });
  
  const passedCount = results.filter(r => r.passed).length;
  console.log(`\n测试完成: ${passedCount}/${results.length} 通过`);
  
  return results;
};

// 在开发环境中自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保所有模块都已加载
  setTimeout(() => {
    console.log('🚀 DDoS & CC 攻击流量图生成器 - 功能测试');
    runAllTests();
  }, 1000);
}
