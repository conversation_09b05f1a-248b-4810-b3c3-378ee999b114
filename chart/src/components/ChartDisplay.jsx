import React from 'react';
import { Card, CardBody } from '@heroui/react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { generateChartData, generateYAxisConfig } from '../utils/chartUtils.js';
import { formatDateTime, generateTimePoints } from '../utils/timeUtils.js';

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ChartDisplay = ({ config }) => {
  const chartData = generateChartData(config);
  const yAxisConfig = generateYAxisConfig(config.attackType, config.peakValue);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: 12,
            family: 'system-ui, -apple-system, sans-serif'
          },
          color: '#374151'
        }
      },
      title: {
        display: true,
        text: `${config.attackType === 'DDOS' ? 'DDoS' : 'CC'} 攻击流量图`,
        font: {
          size: 16,
          weight: 'bold',
          family: 'system-ui, -apple-system, sans-serif'
        },
        color: '#1f2937',
        padding: 20
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#374151',
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: false,
        callbacks: {
          title: function(context) {
            const timeIndex = context[0].dataIndex;
            const timePoints = generateTimePoints(config.chartStartTime, config.chartEndTime);
            return formatDateTime(timePoints[timeIndex]);
          },
          label: function(context) {
            const value = context.parsed.y;
            const unit = chartData.unit;
            
            if (config.attackType === 'CC' && value >= 1000000) {
              return `${(value / 1000000).toFixed(2)}M ${unit}`;
            }
            
            return `${value.toFixed(2)} ${unit}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: '时间',
          font: {
            size: 12,
            weight: 'bold'
          },
          color: '#374151'
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          maxTicksLimit: 12
        },
        grid: {
          color: '#e5e7eb',
          lineWidth: 1
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: `流量 (${chartData.unit})`,
          font: {
            size: 12,
            weight: 'bold'
          },
          color: '#374151'
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          ...yAxisConfig.ticks
        },
        grid: {
          color: '#e5e7eb',
          lineWidth: 1
        },
        ...yAxisConfig
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    },
    elements: {
      line: {
        tension: 0.4
      },
      point: {
        radius: 0,
        hoverRadius: 4,
        hitRadius: 10
      }
    }
  };

  return (
    <Card className="w-full">
      <CardBody className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <div className="w-1 h-6 bg-red-500 rounded"></div>
          <h3 className="text-lg font-semibold text-gray-800">攻击流量图</h3>
        </div>

        {/* 图表信息 */}
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">攻击类型：</span>
              <span className="font-medium text-gray-800">
                {config.attackType === 'DDOS' ? 'DDoS' : 'CC'}
                {config.domain && ` (${config.domain})`}
              </span>
            </div>
            <div>
              <span className="text-gray-600">峰值流量：</span>
              <span className="font-medium text-gray-800">
                {config.attackType === 'CC' && config.peakValue >= 1000000
                  ? `${(config.peakValue / 1000000).toFixed(1)}M ${chartData.unit}`
                  : `${config.peakValue.toLocaleString()} ${chartData.unit}`
                }
              </span>
            </div>
            <div>
              <span className="text-gray-600">攻击时间：</span>
              <span className="font-medium text-gray-800">
                {formatDateTime(config.attackStartTime).split(' ')[1]} ~ {formatDateTime(config.attackEndTime).split(' ')[1]}
              </span>
            </div>
            <div>
              <span className="text-gray-600">波动程度：</span>
              <span className="font-medium text-gray-800">
                {config.fluctuationLevel === 'LOW' ? '低' :
                 config.fluctuationLevel === 'MEDIUM' ? '中' :
                 config.fluctuationLevel === 'HIGH' ? '高' : '极高'}
              </span>
            </div>
          </div>
        </div>

        {/* 图表容器 */}
        <div className="h-96 w-full">
          <Line data={chartData} options={options} />
        </div>

        {/* 图表说明 */}
        <div className="mt-4 text-sm text-gray-600">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-0.5 bg-red-500"></div>
              <span>攻击流量</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-0.5 bg-red-100"></div>
              <span>填充区域表示攻击时间段</span>
            </div>
          </div>
          <div className="mt-2">
            数据点间隔：30分钟 | 
            {config.attackType === 'DDOS' 
              ? ' DDoS攻击特征：瞬间达到峰值' 
              : ' CC攻击特征：逐渐增长到峰值'
            }
          </div>
        </div>
      </CardBody>
    </Card>
  );
};



export default ChartDisplay;
